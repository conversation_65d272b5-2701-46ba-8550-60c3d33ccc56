export function createDashboardPage() {
  return `
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-xl font-semibold text-gray-900">PetaTalenta</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button onclick="navigateTo('profile')" class="text-gray-500 hover:text-gray-700">
                Profile
              </button>
              <button onclick="logout()" class="text-gray-500 hover:text-gray-700">
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">Dashboard</h2>
            <p class="mt-1 text-sm text-gray-600">
              Selamat datang di platform pemetaan talenta
            </p>
          </div>

          <!-- Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-indigo-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">A</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        Assessment Selesai
                      </dt>
                      <dd class="text-lg font-medium text-gray-900">
                        3
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">R</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        Hasil Tersedia
                      </dt>
                      <dd class="text-lg font-medium text-gray-900">
                        3
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">P</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">
                        Dalam Proses
                      </dt>
                      <dd class="text-lg font-medium text-gray-900">
                        1
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- New Assessment Button -->
          <div class="mb-6">
            <button onclick="navigateTo('assessment')"
              class="bg-indigo-600 text-white py-3 px-6 rounded-md hover:bg-indigo-700 transition duration-200 w-full sm:w-auto">
              🚀 Mulai Assessment Baru
            </button>
            <p class="text-sm text-gray-500 mt-1">Assessment komprehensif dengan 3 fase: Big Five, RIASEC, dan VIA Character Strengths</p>
          </div>

          <!-- Development Tools (for testing) -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-yellow-800 mb-2">🧪 Development Tools</h4>
            <div class="flex flex-wrap gap-2">
              <button onclick="testAssessmentResults()" class="bg-yellow-500 text-white py-1 px-3 rounded text-xs hover:bg-yellow-600 transition duration-200">
                Generate Sample Results
              </button>
              <button onclick="showAssessmentFormat()" class="bg-yellow-500 text-white py-1 px-3 rounded text-xs hover:bg-yellow-600 transition duration-200">
                Show Result Format
              </button>
              <button onclick="navigateTo('result')" class="bg-gray-500 text-white py-1 px-3 rounded text-xs hover:bg-gray-600 transition duration-200">
                View Results Page
              </button>
            </div>
            <p class="text-yellow-700 text-xs mt-1">
              Tools untuk testing format hasil assessment. Check console untuk detail.
            </p>
          </div>

          <!-- Assessment Results Table -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Riwayat Assessment
              </h3>
              <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Daftar assessment yang telah Anda selesaikan
              </p>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nama Assessment
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Archetype
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tanggal
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Personality Assessment
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Selesai
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        The Analyst
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      15 Juli 2025
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button onclick="viewResult('personality-001')" class="text-indigo-600 hover:text-indigo-900 mr-3">
                        Lihat Detail
                      </button>
                      <button onclick="downloadResult('personality-001')" class="text-green-600 hover:text-green-900">
                        Download
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Leadership Assessment
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Selesai
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        The Visionary
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      10 Juli 2025
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button onclick="viewResult('leadership-002')" class="text-indigo-600 hover:text-indigo-900 mr-3">
                        Lihat Detail
                      </button>
                      <button onclick="downloadResult('leadership-002')" class="text-green-600 hover:text-green-900">
                        Download
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Career Aptitude Test
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Selesai
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        The Innovator
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      05 Juli 2025
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button onclick="viewResult('career-003')" class="text-indigo-600 hover:text-indigo-900 mr-3">
                        Lihat Detail
                      </button>
                      <button onclick="downloadResult('career-003')" class="text-green-600 hover:text-green-900">
                        Download
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Skills Assessment
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Dalam Proses
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="text-sm text-gray-400">-</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      20 Juli 2025
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button onclick="continueAssessment('skills-004')" class="text-orange-600 hover:text-orange-900">
                        Lanjutkan
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Helper functions for table actions
function viewResult(assessmentId) {
  window.location.hash = `#result/${assessmentId}`;
}

function downloadResult(assessmentId) {
  // Implementation for downloading result
  console.log('Downloading result for:', assessmentId);
  // You can implement actual download logic here
}

function continueAssessment(assessmentId) {
  window.location.hash = `#assessment/${assessmentId}`;
}

export function logout() {
  // Clear session/token
  // Note: In Claude.ai artifacts, localStorage is not available
  // localStorage.removeItem('userToken');
  window.location.hash = '#auth';
}

// Test function to generate and save sample assessment results
export function testAssessmentResults() {
  import('../utils/sampleResults.js').then(({ saveSampleResults, testScoring }) => {
    console.log('Testing assessment results...');

    // Save sample results
    const sampleResults = saveSampleResults();
    console.log('Generated sample results:', sampleResults);

    // Test the scoring system
    testScoring();

    alert('Sample assessment results generated! Check console for details. You can now view results page.');
  });
}

// Test function to show current assessment format
export function showAssessmentFormat() {
  const format = {
    "assessmentName": "AI-Driven Talent Mapping",
    "riasec": {
      "realistic": "0-100",
      "investigative": "0-100",
      "artistic": "0-100",
      "social": "0-100",
      "enterprising": "0-100",
      "conventional": "0-100"
    },
    "ocean": {
      "openness": "0-100",
      "conscientiousness": "0-100",
      "extraversion": "0-100",
      "agreeableness": "0-100",
      "neuroticism": "0-100"
    },
    "viaIs": {
      "creativity": "0-100",
      "curiosity": "0-100",
      "judgment": "0-100",
      "loveOfLearning": "0-100",
      "perspective": "0-100",
      "bravery": "0-100",
      "perseverance": "0-100",
      "honesty": "0-100",
      "zest": "0-100",
      "love": "0-100",
      "kindness": "0-100",
      "socialIntelligence": "0-100",
      "teamwork": "0-100",
      "fairness": "0-100",
      "leadership": "0-100",
      "forgiveness": "0-100",
      "humility": "0-100",
      "prudence": "0-100",
      "selfRegulation": "0-100",
      "appreciationOfBeauty": "0-100",
      "gratitude": "0-100",
      "hope": "0-100",
      "humor": "0-100",
      "spirituality": "0-100"
    }
  };

  console.log('Assessment Result Format:', format);
  console.log('JSON Format:', JSON.stringify(format, null, 2));
  alert('Assessment format logged to console. Check developer tools.');
}