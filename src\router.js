// Import all page modules
import { createAuthPage, handleLogin, showRegister } from './pages/auth.js';
import { createDashboardPage, logout } from './pages/dashboard.js';
import { createAssessment3PhasePage, initAssessment3Phase, nextQuestion as nextQuestion3Phase, previousQuestion as previousQuestion3Phase, skipQuestion, jumpToPhase, jumpToCategory, toggleSidebar, handleAnswerChange, saveAndExit } from './pages/assessment-3phase.js';
import { createWaitingPage, initWaiting, checkResults, cleanupWaiting } from './pages/waiting.js';
import { createResultPage, initResult, downloadResult, shareResult, retakeAssessment, scheduleConsultation } from './pages/result.js';
import { createProfilePage, initProfile, saveProfile, changeAvatar, deleteAccount } from './pages/profile.js';

// Page configurations
const pages = {
  auth: {
    component: createAuthPage,
    init: null,
    cleanup: null,
    requiresAuth: false
  },
  dashboard: {
    component: createDashboardPage,
    init: null,
    cleanup: null,
    requiresAuth: true
  },
  assessment: {
    component: createAssessment3PhasePage,
    init: initAssessment3Phase,
    cleanup: null,
    requiresAuth: true
  },
  waiting: {
    component: createWaitingPage,
    init: initWaiting,
    cleanup: cleanupWaiting,
    requiresAuth: true
  },
  result: {
    component: createResultPage,
    init: initResult,
    cleanup: null,
    requiresAuth: true
  },
  profile: {
    component: createProfilePage,
    init: initProfile,
    cleanup: null,
    requiresAuth: true
  }
};

let currentPage = null;

// Router class
class Router {
  constructor() {
    this.currentRoute = null;
    this.init();
  }

  init() {
    // Listen for hash changes
    window.addEventListener('hashchange', () => {
      this.handleRoute();
    });

    // Handle initial route
    this.handleRoute();
  }

  handleRoute() {
    const hash = window.location.hash.slice(1) || 'auth';
    this.navigateTo(hash);
  }

  navigateTo(route) {
    const page = pages[route];
    
    if (!page) {
      console.error(`Page "${route}" not found`);
      this.navigateTo('auth');
      return;
    }

    // Check authentication
    if (page.requiresAuth && !this.isAuthenticated()) {
      this.navigateTo('auth');
      return;
    }

    // Cleanup previous page
    if (currentPage && currentPage.cleanup) {
      currentPage.cleanup();
    }

    // Update current page
    currentPage = page;
    this.currentRoute = route;

    // Render page
    this.renderPage(page);

    // Initialize page
    if (page.init) {
      page.init();
    }

    // Update URL hash
    if (window.location.hash.slice(1) !== route) {
      window.location.hash = route;
    }
  }

  renderPage(page) {
    const app = document.getElementById('app');
    if (app) {
      app.innerHTML = page.component();
    }
  }

  isAuthenticated() {
    // Simple authentication check
    // In real app, this would check JWT token, session, etc.
    return localStorage.getItem('userToken') || localStorage.getItem('isLoggedIn');
  }

  login(userData) {
    // Simulate login
    localStorage.setItem('isLoggedIn', 'true');
    localStorage.setItem('userToken', 'dummy-token');
    if (userData) {
      localStorage.setItem('userData', JSON.stringify(userData));
    }
    this.navigateTo('dashboard');
  }

  logout() {
    // Clear authentication data
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    this.navigateTo('auth');
  }

  getCurrentRoute() {
    return this.currentRoute;
  }
}

// Create router instance
const router = new Router();

// Global navigation function
window.navigateTo = (route) => {
  router.navigateTo(route);
};

// Global functions for pages
window.handleLogin = (event) => {
  event.preventDefault();
  const formData = new FormData(event.target);
  const email = formData.get('email');
  const password = formData.get('password');
  
  // Simple validation
  if (!email || !password) {
    alert('Silakan isi email dan password');
    return;
  }

  // Simulate login process
  console.log('Login attempt:', { email, password });
  
  // In real app, this would make API call
  // For demo, we'll just simulate success
  router.login({ email, name: email.split('@')[0] });
};

window.showRegister = showRegister;
window.logout = () => router.logout();

// Assessment functions
window.nextQuestion = nextQuestion3Phase;
window.previousQuestion = previousQuestion3Phase;
window.skipQuestion = skipQuestion;
window.jumpToPhase = jumpToPhase;
window.jumpToCategory = jumpToCategory;
window.toggleSidebar = toggleSidebar;
window.handleAnswerChange = handleAnswerChange;
window.saveAndExit = saveAndExit;

// Waiting functions
window.checkResults = checkResults;

// Result functions
window.downloadResult = downloadResult;
window.shareResult = shareResult;
window.retakeAssessment = retakeAssessment;
window.scheduleConsultation = scheduleConsultation;

// Profile functions
window.saveProfile = saveProfile;
window.changeAvatar = changeAvatar;
window.deleteAccount = deleteAccount;

export default router;
