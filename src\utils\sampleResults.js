// Sample Assessment Results Generator
// Generates sample results in the specified format for testing

export function generateSampleResults() {
  return {
    "assessmentName": "AI-Driven Talent Mapping",
    "riasec": {
      "realistic": 75,
      "investigative": 80,
      "artistic": 65,
      "social": 70,
      "enterprising": 85,
      "conventional": 60
    },
    "ocean": {
      "openness": 80,
      "conscientiousness": 75,
      "extraversion": 70,
      "agreeableness": 85,
      "neuroticism": 40
    },
    "viaIs": {
      "creativity": 80,
      "curiosity": 85,
      "judgment": 75,
      "loveOfLearning": 90,
      "perspective": 70,
      "bravery": 65,
      "perseverance": 80,
      "honesty": 85,
      "zest": 75,
      "love": 80,
      "kindness": 85,
      "socialIntelligence": 75,
      "teamwork": 80,
      "fairness": 85,
      "leadership": 70,
      "forgiveness": 75,
      "humility": 80,
      "prudence": 75,
      "selfRegulation": 80,
      "appreciationOfBeauty": 70,
      "gratitude": 85,
      "hope": 80,
      "humor": 75,
      "spirituality": 60
    },
    "completedAt": new Date().toISOString(),
    "totalQuestions": 200
  };
}

// Generate sample answers for testing scoring
export function generateSampleAnswers() {
  const answers = {};
  
  // Big Five questions (1-44)
  for (let i = 1; i <= 44; i++) {
    answers[i] = Math.floor(Math.random() * 7) + 1; // Random 1-7
  }
  
  // RIASEC questions (1000-1059)
  for (let i = 1000; i <= 1059; i++) {
    answers[i] = Math.floor(Math.random() * 7) + 1; // Random 1-7
  }
  
  // VIA-IS questions (2000-2095)
  for (let i = 2000; i <= 2095; i++) {
    answers[i] = Math.floor(Math.random() * 7) + 1; // Random 1-7
  }
  
  return answers;
}

// Test the scoring system with sample data
export function testScoring() {
  import('./assessmentScoring.js').then(({ assessmentScoring }) => {
    const sampleAnswers = generateSampleAnswers();
    const results = assessmentScoring.calculateAssessmentResults(sampleAnswers);
    
    console.log('Sample Assessment Results:', results);
    console.log('Formatted JSON:', JSON.stringify(results, null, 2));
    
    return results;
  });
}

// Save sample results for testing
export function saveSampleResults() {
  const sampleResults = generateSampleResults();
  localStorage.setItem('assessmentResults', JSON.stringify(sampleResults));
  localStorage.setItem('assessmentResultReady', 'true');
  
  console.log('Sample results saved to localStorage:', sampleResults);
  return sampleResults;
}
