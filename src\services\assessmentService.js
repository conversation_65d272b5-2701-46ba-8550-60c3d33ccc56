// Assessment Service for API communication
// Handles saving and retrieving assessment results

export class AssessmentService {
  constructor() {
    // In a real application, this would come from environment variables
    this.baseURL = process.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
    this.endpoints = {
      saveAssessment: '/assessments',
      getAssessment: '/assessments',
      getUserAssessments: '/users/assessments'
    };
  }

  // Get authentication headers
  getAuthHeaders() {
    const token = localStorage.getItem('userToken');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }

  // Save assessment results to backend
  async saveAssessmentResults(assessmentData) {
    try {
      // Add user information if available
      const userData = JSON.parse(localStorage.getItem('userData') || '{}');
      const payload = {
        ...assessmentData,
        userId: userData.id || null,
        userEmail: userData.email || null
      };

      const response = await fetch(`${this.baseURL}${this.endpoints.saveAssessment}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // Store the assessment ID locally for future reference
      localStorage.setItem('lastAssessmentId', result.id || result.assessmentId);
      
      return {
        success: true,
        data: result,
        message: 'Assessment results saved successfully'
      };

    } catch (error) {
      console.error('Error saving assessment results:', error);
      
      // Fallback: save to localStorage if API fails
      const fallbackData = {
        ...assessmentData,
        id: `local_${Date.now()}`,
        savedAt: new Date().toISOString(),
        source: 'localStorage'
      };
      
      localStorage.setItem('assessmentResults', JSON.stringify(fallbackData));
      localStorage.setItem('lastAssessmentId', fallbackData.id);
      
      return {
        success: false,
        error: error.message,
        fallbackSaved: true,
        data: fallbackData,
        message: 'Assessment saved locally due to connection issues'
      };
    }
  }

  // Get assessment results by ID
  async getAssessmentResults(assessmentId) {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getAssessment}/${assessmentId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error fetching assessment results:', error);
      
      // Fallback: try to get from localStorage
      const localData = localStorage.getItem('assessmentResults');
      if (localData) {
        const parsedData = JSON.parse(localData);
        if (parsedData.id === assessmentId) {
          return {
            success: true,
            data: parsedData,
            source: 'localStorage'
          };
        }
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get all assessments for current user
  async getUserAssessments() {
    try {
      const response = await fetch(`${this.baseURL}${this.endpoints.getUserAssessments}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error fetching user assessments:', error);
      
      // Fallback: return local assessment if available
      const localData = localStorage.getItem('assessmentResults');
      if (localData) {
        return {
          success: true,
          data: [JSON.parse(localData)],
          source: 'localStorage'
        };
      }
      
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  // Retry saving assessment (for offline scenarios)
  async retrySaveAssessment() {
    const localData = localStorage.getItem('assessmentResults');
    if (!localData) {
      return { success: false, message: 'No local assessment data found' };
    }

    const assessmentData = JSON.parse(localData);
    if (assessmentData.source !== 'localStorage') {
      return { success: false, message: 'Assessment already saved to server' };
    }

    // Remove local-specific fields before sending to server
    const { id, savedAt, source, ...serverData } = assessmentData;
    
    return await this.saveAssessmentResults(serverData);
  }

  // Check if there are unsaved local assessments
  hasUnsavedAssessments() {
    const localData = localStorage.getItem('assessmentResults');
    if (!localData) return false;
    
    const assessmentData = JSON.parse(localData);
    return assessmentData.source === 'localStorage';
  }

  // Clear local assessment data
  clearLocalAssessmentData() {
    localStorage.removeItem('assessmentResults');
    localStorage.removeItem('lastAssessmentId');
    localStorage.removeItem('assessment3PhaseAnswers');
    localStorage.removeItem('assessment3PhaseCompleted');
    localStorage.removeItem('assessment3PhaseState');
  }
}

// Create singleton instance
export const assessmentService = new AssessmentService();
